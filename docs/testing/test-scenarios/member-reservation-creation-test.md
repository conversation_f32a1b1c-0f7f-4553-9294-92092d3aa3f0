# Member User Reservation Creation Test Scenario

> **Note**: This documentation has been updated to reflect current role terminology and implementation. The system now uses `member` role instead of `normal_user`.

## Test Scenario Overview

**Test ID:** EMP-RES-001
**Test Title:** Member User Creates New Field Reservation
**Test Type:** Functional Test
**Priority:** High
**Estimated Duration:** 15-20 minutes

**Description:**
This test validates that a Member user can successfully create a new field reservation in the FPMP Online system, including field selection, duration-based time slot booking, customer information entry, and optional utility additions. The test verifies the complete reservation workflow from login through confirmation, ensuring all member-specific features work correctly with server-side cost calculation.

## Prerequisites

### System Requirements
- SMP Online system is running and accessible
- Database is seeded with test data
- All field types are active and available
- At least 2-3 utilities are active in the system

### User Account Requirements
- Member user account exists with the following credentials:
  - **Username:** `<EMAIL>`
  - **Password:** `password`
  - **Role:** `member`
  - **Status:** Active (not locked)

### Test Data Setup
- At least 3 active fields with different types (Soccer, Basketball, Bolas)
- Fields should have varying hourly rates and booking constraints
- Active utilities available (e.g., Tables, Chairs, Lighting, Sound)
- No conflicting reservations for the test date/time slots

## Test Data

### Field Information (Expected Available Fields)
- **Field 1:** Soccer Field A
  - Type: Soccer
  - Hourly Rate: XCG 75.00
  - Capacity: 22 players
  - Min Booking: 1 hour
  - Max Booking: 4 hours
  - Business Hours: 08:00 - 22:00

- **Field 2:** Basketball Court B
  - Type: Basketball
  - Hourly Rate: XCG 50.00
  - Capacity: 10 players
  - Min Booking: 1 hour
  - Max Booking: 3 hours
  - Business Hours: 08:00 - 22:00

### Reservation Details
- **Booking Date:** Tomorrow's date (ensure it's a future date)
- **Start Time:** 14:00 (2:00 PM)
- **Duration:** 2.0 hours (supports half-hour increments)
- **End Time:** 16:00 (4:00 PM) - Auto-calculated based on start time + duration

### Customer Information
- **Customer Name:** John Smith
- **Customer Email:** <EMAIL>
- **Customer Phone:** ******-0123
- **Special Requests:** "Please ensure field is well-lit and equipment is ready"

### Utility Selection (Optional)
- **Utility 1:** Lighting System
  - Rate: XCG 15.00/unit
  - Quantity: 2 units
  - Cost: XCG 30.00

- **Utility 2:** Sound System
  - Rate: XCG 25.00/unit
  - Quantity: 2 units
  - Cost: XCG 50.00

**Note**: Utilities are now quantity-based rather than hour-based, though the form field may still show "hours" for backward compatibility.

## Step-by-Step Instructions

### Phase 1: Login and Navigation

1. **Navigate to Login Page**
   - Open browser and go to the SMP Online login page
   - Verify the login form is displayed correctly

2. **Member Login**
   - Enter email: `<EMAIL>`
   - Enter password: `password`
   - Click "Sign In" button
   - **Expected Result:** Successfully redirected to Member Dashboard

3. **Verify Member Dashboard Access**
   - Confirm page title shows "Member Dashboard"
   - Verify welcome message displays member name
   - Confirm member-specific features are visible
   - **Expected Result:** Member dashboard loads with appropriate permissions

4. **Navigate to Reservation Creation**
   - Click on "New Reservation" button in the "Reservations" card
   - **Expected Result:** Redirected to reservation creation form

### Phase 2: Field Selection and Scheduling

5. **Access Reservation Form**
   - Verify the "Create New Reservation" form loads
   - Confirm all required form sections are visible:
     - Field Selection
     - Date & Time
     - Customer Information
     - Utilities (optional)
   - **Expected Result:** Complete reservation form is displayed

6. **Select Field**
   - Click on the "Field" dropdown
   - Verify all active fields are listed with rates
   - Select "Soccer Field A" (or first available field)
   - **Expected Result:** Field information updates showing rate and capacity

7. **Set Date**
   - Click on the "Date" field
   - Select tomorrow's date from date picker
   - Ensure selected date is in the future
   - **Expected Result:** Date is selected and availability check triggers

8. **Select Start Time**
   - Click on "Start Time" dropdown
   - Verify available start times are displayed (30-minute increments)
   - Select "14:00" (2:00 PM)
   - **Expected Result:** Start time is selected and duration selection is enabled

9. **Select Duration**
   - Click on "Duration" dropdown
   - Verify available durations are displayed (supports half-hour increments: 1.0, 1.5, 2.0, 2.5, etc.)
   - Select "2" hours
   - **Expected Result:** Duration is selected, end time is auto-calculated (16:00), availability check triggers, cost calculation is updated

### Phase 3: Utility Addition (Optional)

12. **Add First Utility**
    - Click "Add Utility" dropdown
    - Select "Lighting System"
    - Set hours to "2"
    - Click "Add" button
    - **Expected Result:** Utility added to list, total cost recalculated

13. **Add Second Utility**
    - Select "Sound System" from utility dropdown
    - Set hours to "2"
    - Click "Add" button
    - **Expected Result:** Second utility added, total cost recalculated

### Phase 4: Customer Information Entry

10. **Enter Customer Details**
    - Fill in "Customer Name": "John Smith"
    - Fill in "Customer Email": "<EMAIL>"
    - Fill in "Customer Phone": "******-0123"
    - **Expected Result:** All customer fields populated correctly

11. **Add Special Requests**
    - Click in "Special Requests" text area
    - Enter: "Please ensure field is well-lit and equipment is ready"
    - **Expected Result:** Special requests text is entered and saved

### Phase 5: Validation and Submission

14. **Review Reservation Summary**
    - Verify all entered information is correct
    - Check total cost calculation:
      - Field cost: XCG 150.00 (75.00 × 2 hours)
      - Lighting: XCG 30.00 (15.00 × 2 hours)
      - Sound: XCG 50.00 (25.00 × 2 hours)
      - **Total Expected:** XCG 230.00
    - **Expected Result:** All details are accurate and cost is correct

15. **Submit Reservation**
    - Click "Create Reservation" button
    - **Expected Result:** Form submits successfully

### Phase 6: Confirmation and Verification

16. **Verify Success Message**
    - Confirm success message appears
    - Message should indicate "Reservation created successfully!"
    - Note: Reservations are automatically set to pending until confirmed
    - **Expected Result:** Success message displayed with pending status

17. **Check Reservation Details**
    - Verify reservation appears in reservation list
    - Confirm status is "Pending"
    - Check all details match entered information
    - **Expected Result:** Reservation created with "Pending" status

## Expected Results

### Successful Reservation Creation
- Reservation is created in the database with status "Pending"
- Reservations are automatically set to pending until confirmed
- All customer information is saved correctly
- Selected utilities are attached with correct pricing
- Total cost is calculated accurately including utilities
- Reservation appears in reservation list
- Field availability is updated to reflect the booking

### Database Validation Points
- New record in `reservations` table with correct data
- `status` field set to "Pending"
- `confirmed_at` timestamp is null
- `user_id` matches the logged-in member
- Utility relationships created in `reservation_utility` pivot table

### UI/UX Validation Points
- Form validation works for required fields
- Real-time cost calculation updates correctly
- Time slot availability updates dynamically
- Success message displays appropriate content for member role
- Navigation remains consistent throughout the process

## Validation Points

### Critical Validation Checks

1. **Authentication & Authorization**
   - Member can access reservation creation
   - Member role permissions are enforced
   - Session management works correctly

2. **Business Logic Validation**
   - Only future dates can be selected
   - Time slots respect business hours (8 AM - 10 PM)
   - Start/End Time respects field min/max constraints
   - Availability checking prevents double-booking
   - Cost calculation includes all components

3. **Member-Specific Features**
   - Reservations are automatically set to pending until confirmed
   - Confirmation approval workflow is required for pending reservations
   - Member can book on behalf of customers
   - Access to all active fields and utilities

4. **Data Integrity**
   - All form data is saved correctly
   - Relationships between reservation and utilities are maintained
   - Timestamps are recorded accurately
   - Field availability is updated in real-time

## Edge Cases and Variations

### Alternative Test Scenarios

1. **Reservation Conflict Resolution**
   - Attempt to book an already reserved time slot
   - Verify appropriate error message
   - Confirm alternative time slots are suggested

2. **Validation Error Handling**
   - Submit form with missing required fields
   - Enter invalid email format
   - Select past date
   - Exceed maximum booking duration

3. **Utility Management**
   - Add and remove utilities multiple times
   - Verify cost recalculation accuracy
   - Test with no utilities selected

4. **Field Constraints Testing**
   - Test minimum booking duration enforcement
   - Test maximum booking duration limits
   - Verify business hours restrictions

5. **Session and Security**
   - Test session timeout during form completion
   - Verify CSRF protection
   - Test concurrent booking attempts

### Browser Compatibility
- Test on Chrome, Firefox, Safari, Edge
- Verify mobile responsiveness
- Check JavaScript functionality across browsers

### Performance Considerations
- Form should load within 3 seconds
- Availability checking should respond within 2 seconds
- Submission should complete within 5 seconds

## Post-Test Cleanup

1. **Data Cleanup**
   - Remove test reservation from database
   - Reset field availability
   - Clear any test utilities added

2. **System State**
   - Logout member user
   - Verify system returns to clean state
   - Confirm no residual data affects subsequent tests

## Notes for Test Execution

- Execute during off-peak hours to avoid conflicts
- Have database backup available before testing
- Document any deviations from expected behavior
- Take screenshots of key steps for documentation
- Record actual vs expected timing for performance analysis

---

**Test Created By:** QA Team  
**Date Created:** 2025-07-21  
**Last Updated:** 2025-07-21  
**Version:** 1.0
