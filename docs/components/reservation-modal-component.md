# Reservation Details Modal Component

## Overview

The Reservation Details Modal is a reusable Blade component that displays comprehensive reservation information in a modal overlay. It provides a seamless user experience for viewing reservation details without navigating away from the current page.

## Files Structure

```
resources/views/components/reservation-details-modal.blade.php  # Blade component
public/assets/js/reservation-modal.js                          # JavaScript functions
```

## Usage

### 1. Include the Component

Add the component to any Blade view:

```blade
<x-reservation-details-modal />
```

### 2. Include the JavaScript

Include the JavaScript file after Bootstrap JS:

```blade
<script src="{{ asset('assets/js/reservation-modal.js') }}"></script>
```

### 3. Open the Modal

Call the JavaScript function to open the modal:

```javascript
openReservationModal(reservationId);
```

## Features

- **AJAX Data Loading**: Fetches reservation data dynamically
- **Loading States**: Shows spinner while loading data
- **Error Handling**: Displays error message if loading fails
- **Responsive Design**: Works on all screen sizes (modal-xl)
- **Dark Mode Support**: Consistent with Ynex admin template
- **Action Buttons**: Edit/Cancel buttons based on user permissions
- **Scrollable Content**: Handles long reservation details
- **Cost Breakdown**: Detailed server-calculated cost information

## JavaScript Functions

### `openReservationModal(reservationId)`
Opens the modal and loads reservation data via AJAX.

**Parameters:**
- `reservationId` (number): The ID of the reservation to display

### `renderReservationDetails(reservation)`
Renders reservation data in the modal content area.

**Parameters:**
- `reservation` (object): The reservation data object from the API

### `renderCostBreakdown(reservation)`
Generates HTML for the cost breakdown section.

**Parameters:**
- `reservation` (object): The reservation data object

**Returns:**
- `string`: HTML string for the cost breakdown

### `updateModalFooter(reservation)`
Updates the modal footer with action buttons based on permissions.

**Parameters:**
- `reservation` (object): The reservation data object

### `cancelReservation(cancelUrl)`
Handles reservation cancellation by submitting a form.

**Parameters:**
- `cancelUrl` (string): The URL to submit the cancellation request

## API Endpoint

The modal expects a JSON API endpoint at:
```
GET /reservations/{id}/details
```

The endpoint should return reservation data including:
- Basic reservation info (id, status, dates, times)
- Field information (name, type, capacity, rates)
- Customer details (name, email, phone)
- Cost breakdown and utilities
- Action permissions and URLs

## Dependencies

- **Bootstrap 5**: For modal functionality
- **CSRF Token**: Meta tag in page head for security
- **Ynex Admin Template**: For consistent styling

## Example Implementation

```blade
{{-- In your view file --}}
@extends('layouts.admin')

@section('content')
    <!-- Your page content -->
    <div class="calendar-container">
        <!-- Calendar or other content -->
    </div>

    <!-- Include the modal component -->
    <x-reservation-details-modal />
@endsection

@section('scripts')
    <!-- Include the JavaScript -->
    <script src="{{ asset('assets/js/reservation-modal.js') }}"></script>

    <script>
        // Example: Open modal when clicking a reservation
        document.querySelectorAll('.reservation-item').forEach(item => {
            item.addEventListener('click', function() {
                const reservationId = this.dataset.reservationId;
                openReservationModal(reservationId);
            });
        });
    </script>
@endsection
```

## Real-World Usage

The component is currently used in:
- **Calendar View** (`resources/views/calendar/index.blade.php`) - Displays reservation details when clicking on calendar events
- **FullCalendar Integration** - Handles `eventClick` events to show reservation details in modal format

## Styling

The component includes built-in dark mode support and responsive design. All styling is contained within the component file for easy maintenance.

## Security

- Uses CSRF tokens for all requests
- Maintains existing authorization checks
- Server-side data validation and sanitization

## Browser Support

Compatible with all modern browsers that support:
- ES6 JavaScript features
- Bootstrap 5 modal functionality
- CSS Grid and Flexbox
