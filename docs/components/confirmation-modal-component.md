# Confirmation Modal Component Documentation

## Overview

The `confirmation-modal` component provides a standardized, reusable confirmation modal for various actions across the admin interface. It features color-coded modal types, consistent styling, and flexible configuration options while maintaining excellent user experience.

## Component Location

```
resources/views/components/confirmation-modal.blade.php
```

## Features

- ✅ **Color-Coded Modal Types**: 4 distinct types (Success, Info, Warning, Danger) with consistent theming
- ✅ **New Header Layout**: Icon and title positioned horizontally with proper spacing
- ✅ **Improved Content Alignment**: Body text aligned with title for better visual hierarchy
- ✅ **Enhanced Button Styling**: Outline cancel button and solid confirm button with type-specific colors
- ✅ **Horizontal Separator**: Clear visual separation between body and footer
- ✅ **Centered modal** with smooth fade-in animation (`effect-scale`)
- ✅ **CSRF protection** and HTTP method spoofing support
- ✅ **Bootstrap 5 compatible** with modern spacing and layout
- ✅ **Responsive design** with proper mobile support
- ✅ **Accessible** with proper ARIA labels
- ✅ **Backward compatible** with existing modal usage

## Modal Types

### 1. Success (Green)
- **Icon**: `ri-check-line`
- **Title**: "Confirm Action"
- **Use Cases**: Approvals, activations, confirmations

### 2. Info (Blue)
- **Icon**: `ri-information-line`
- **Title**: "Information"
- **Use Cases**: Notifications, information actions, neutral confirmations

### 3. Warning (Yellow)
- **Icon**: `ri-alert-line`
- **Title**: "Warning"
- **Use Cases**: Archive actions, status changes, cautionary operations

### 4. Danger (Red) - Default
- **Icon**: `ri-error-warning-line`
- **Title**: "Confirm Deletion"
- **Use Cases**: Deletions, destructive actions, permanent changes

## Parameters

| Parameter | Type | Default | Required | Description |
|-----------|------|---------|----------|-------------|
| `modalId` | string | `'confirmationModal'` | No | Unique ID for the modal |
| `type` | string | `'danger'` | No | Modal type for color theming (`success`, `info`, `warning`, `danger`) |
| `icon` | string | Auto-selected | No | Icon class (auto-selected based on type, can be overridden) |
| `modalTitle` | string | Auto-generated | No | Title text next to icon (auto-generated based on type) |
| `title` | string | `''` | Yes | Main confirmation text (supports HTML) |
| `warningText` | string | `'This action cannot be undone!'` | No | Warning message text |
| `cancelText` | string | `'No, Cancel'` | No | Cancel button text |
| `confirmText` | string | `'Yes, Confirm'` | No | Confirm button text |
| `dismissText` | string | `''` | No | Dismiss button text for notification mode |
| `formAction` | string | `'#'` | Yes | Form action URL |
| `formMethod` | string | `'DELETE'` | No | HTTP method (POST, DELETE, PATCH, etc.) |
| `targetElementId` | string | `null` | No | ID for dynamic content updates |

## Usage Examples

### 1. Basic Delete Confirmation (Danger - Default)

```blade
<x-confirmation-modal
    modal-id="deleteUserModal"
    title="Are you sure you want to delete this user?"
    form-action="{{ route('admin.users.destroy', $user) }}"
    confirm-text="Yes, Delete User"
/>
```

### 2. Success Modal (Green Theme)

```blade
<x-confirmation-modal
    modal-id="approveModal"
    type="success"
    title="Are you sure you want to approve this request?"
    warning-text="This will activate the user account."
    confirm-text="Yes, Approve"
    form-action="{{ route('admin.users.approve', $user) }}"
    form-method="PATCH"
/>
```

### 3. Warning Modal (Yellow Theme)

```blade
<x-confirmation-modal
    modal-id="archiveModal"
    type="warning"
    title="Are you sure you want to archive this item?"
    warning-text="This item will be moved to the archive."
    cancel-text="Keep Active"
    confirm-text="Yes, Archive"
    form-action="{{ route('admin.items.archive', $item) }}"
    form-method="PATCH"
/>
```

### 4. Info Modal (Blue Theme)

```blade
<x-confirmation-modal
    modal-id="notifyModal"
    type="info"
    title="Send notification to all users?"
    warning-text="This will send an email to all registered users."
    confirm-text="Yes, Send"
    form-action="{{ route('admin.notifications.send') }}"
    form-method="POST"
/>
```

### 5. Notification Modal (Single Button)

```blade
<x-confirmation-modal
    modal-id="successNotificationModal"
    type="success"
    title="Operation completed successfully!"
    warning-text="Your request has been processed."
    dismiss-text="OK"
    form-action="#"
/>
```

### 3. Dynamic Content with JavaScript

```blade
<x-confirmation-modal 
    modal-id="deleteUtilityModal"
    title="Are you sure you want to delete the utility &quot;<span id='utilityName' class='fw-semibold'></span>&quot;?"
    form-action="#"
    confirm-text="Yes, Delete Utility"
/>
```

**JavaScript Integration:**
```javascript
function confirmDelete(itemId, itemName) {
    document.getElementById('utilityName').textContent = itemName;
    document.getElementById('deleteUtilityModalForm').action = `/admin/utilities/${itemId}`;
    const modal = new bootstrap.Modal(document.getElementById('deleteUtilityModal'));
    modal.show();
}
```

### 4. Bulk Actions

```blade
<x-confirmation-modal 
    modal-id="bulkDeleteModal"
    icon="ri-delete-bin-2-line"
    title="Are you sure you want to delete <span id='selectedCount' class='fw-semibold'></span> selected items?"
    warning-text="This will permanently delete all selected items!"
    confirm-text="Yes, Delete All"
    form-action="{{ route('admin.bulk.delete') }}"
/>
```

## Implementation Guide

### Step 1: Add Component to Your View

```blade
@extends('layouts.admin')

@section('content')
    <!-- Your page content -->
    
    <!-- Add the confirmation modal -->
    <x-confirmation-modal 
        modal-id="deleteItemModal"
        title="Are you sure you want to delete this item?"
        form-action="#"
        confirm-text="Yes, Delete Item"
    />
@endsection
```

### Step 2: Add JavaScript Function

```blade
@push('scripts')
<script>
function confirmDelete(itemId, itemName) {
    // Update dynamic content if needed
    if (document.getElementById('itemName')) {
        document.getElementById('itemName').textContent = itemName;
    }
    
    // Set form action
    document.getElementById('deleteItemModalForm').action = `/admin/items/${itemId}`;
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('deleteItemModal'));
    modal.show();
}
</script>
@endpush
```

### Step 3: Add Trigger Button

```blade
<button type="button" 
        class="btn btn-sm btn-danger"
        onclick="confirmDelete({{ $item->id }}, '{{ $item->name }}')">
    <i class="ri-delete-bin-line"></i> Delete
</button>
```

## Form ID Convention

The component automatically generates form IDs using the pattern: `{modalId}Form`

Examples:
- `deleteModal` → `deleteModalForm`
- `archiveUserModal` → `archiveUserModalForm`
- `bulkDeleteModal` → `bulkDeleteModalForm`

## Styling Customization

The component uses Bootstrap 5 classes and can be customized by:

1. **Overriding CSS classes** in your page-specific styles
2. **Modifying icon size** via the `iconSize` parameter
3. **Using different icons** via the `icon` parameter
4. **Custom warning text styling** by modifying the component template

## Best Practices

1. **Use descriptive modal IDs** to avoid conflicts
2. **Include item names** in confirmation text for clarity
3. **Use appropriate icons** for different action types
4. **Test JavaScript integration** thoroughly
5. **Ensure CSRF tokens** are properly handled
6. **Provide clear button text** that describes the action

## Common Use Cases

- User deletion
- Utility/resource removal
- Bulk operations
- Status changes (deactivation)
- Data archiving
- Permission revocation
- Content moderation actions

## Troubleshooting

### Modal Not Showing
- Check that Bootstrap 5 is loaded
- Verify modal ID is unique
- Ensure JavaScript function is called correctly

### Form Not Submitting
- Verify form action URL is correct
- Check CSRF token is present
- Ensure HTTP method is supported by route

### Dynamic Content Not Updating
- Confirm element IDs match between HTML and JavaScript
- Check for JavaScript errors in console
- Verify element exists before updating content
