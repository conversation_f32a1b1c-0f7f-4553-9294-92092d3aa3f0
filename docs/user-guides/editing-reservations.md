# Editing Reservations Guide

## Overview

As a Member, you can edit reservations that you created, provided they meet certain conditions. This guide explains how to find, access, and modify your reservations while understanding the restrictions and policies that apply.

## Member Access Controls

### What You Can Edit
You can only edit reservations that:
- ✅ **You Created**: You must be the member who originally created the reservation
- ✅ **Are Future Bookings**: The reservation date must be in the future
- ✅ **Meet Time Requirements**: Must be at least 24 hours before the scheduled time
- ✅ **Have Correct Status**: Must be "Pending" or "Confirmed" status

### What You Cannot Edit
- ❌ **Other Members' Reservations**: Only admins can edit any reservation
- ❌ **Past Reservations**: Cannot modify completed bookings
- ❌ **Last-Minute Bookings**: Cannot edit within 24 hours of the booking time
- ❌ **Cancelled Reservations**: Must restore first, then edit

## Finding Your Reservations to Edit

### From the Reservations List
1. Go to the **Reservations List** (click "My Reservations" or "View List")
2. Look for reservations with a yellow **edit icon** (pencil) in the Actions column
3. If you don't see the edit icon, the reservation cannot be modified

### From the Calendar
1. Open the **Calendar View**
2. Click on one of your reservations
3. In the popup details, look for an **"Edit Reservation"** button
4. The button only appears if you can edit the reservation

### From Reservation Details
1. Click on any of your reservations to view details
2. At the bottom of the details page, look for action buttons
3. The **"Edit Reservation"** button appears if editing is allowed

## Understanding Edit Restrictions

### 24-Hour Rule
- **Policy**: Reservations cannot be modified within 24 hours of the booking time
- **Example**: If your booking is Tuesday at 2:00 PM, you cannot edit after Monday at 2:00 PM
- **Reason**: Allows adequate notice for facility preparation and customer notification

### Status Requirements
- **Confirmed Reservations**: Can be edited if time requirements are met
- **Pending Reservations**: Can be edited while awaiting approval
- **Cancelled Reservations**: Must be restored before editing
- **Completed Reservations**: Cannot be edited

### Permission Checks
- **Your Reservations**: Full editing access (within time limits)
- **Other Members' Reservations**: No editing access
- **Admin Override**: Admins can edit any reservation

## Step-by-Step Editing Process

### Step 1: Access the Edit Form
1. Find your reservation using one of the methods above
2. Click the **"Edit Reservation"** button or edit icon
3. You'll be taken to the edit form with current details pre-filled

### Step 2: Review Current Information
The edit form shows:
- **Current Field**: The field currently booked
- **Current Date**: The booking date
- **Current Time**: Start time and duration
- **Current Customer**: Customer information
- **Current Utilities**: Selected utilities and quantities
- **Current Cost**: Total cost breakdown

### Step 3: Make Your Changes

**Field Changes:**
- Select a different field from the dropdown
- Note: This may affect availability and cost
- Field information (hours, rates) will update automatically

**Date Changes:**
- Click the date field to open the calendar picker
- Select a new future date
- Availability will be rechecked for the new date

**Time Changes:**
- **Start Time**: Select from available start times
- **Duration**: Choose new duration in hours
- System will check availability for new time slot

**Customer Information Changes:**
- Update customer name, phone, or email
- Modify special requests or comments
- All customer fields can be freely edited

**Utility Changes:**
- Add or remove utilities
- Change quantities for existing utilities
- Cost will update automatically

### Step 4: Availability Checking
- **Real-Time Validation**: System checks availability as you make changes
- **Conflict Detection**: Unavailable times are disabled
- **Alternative Suggestions**: System may suggest nearby available times
- **Sportspark Conflicts**: Individual fields blocked if Sportspark is booked

### Step 5: Cost Recalculation
- **Automatic Updates**: Costs recalculate as you make changes
- **Field Rate Changes**: Different fields may have different rates
- **Utility Adjustments**: Adding/removing utilities affects total cost
- **Final Review**: Check the new total cost before saving

## Saving Your Changes

### Before Submitting
1. **Review All Changes**: Ensure all modifications are correct
2. **Check Availability**: Verify the new time slot is available
3. **Verify Customer Info**: Ensure contact details are accurate
4. **Review Cost**: Confirm the new total cost is acceptable

### Submission Process
1. Click the **"Update Reservation"** button
2. Wait for the system to process your changes
3. You'll see a confirmation message if successful
4. The updated reservation appears in your list

### After Saving
- **Confirmation**: Success message confirms the update
- **Status Maintained**: Reservation keeps its current status
- **Calendar Update**: Changes appear immediately in calendar and list views
- **Notifications**: Customer may receive update notification (if configured)

## Understanding Cost Changes

### When Costs Change
- **Different Field**: New field may have different hourly rates
- **Time Changes**: Different times may have different rates
- **Utility Modifications**: Adding/removing utilities affects cost
- **Duration Changes**: Longer/shorter bookings change total cost

### Cost Calculation
- **Field Cost**: New hourly rate × new duration
- **Utility Costs**: Updated based on new selections
- **Total Cost**: Automatically calculated and displayed
- **Payment**: May require additional payment or refund processing

## Common Editing Scenarios

### Changing the Time
**Scenario**: Customer needs to move their 2:00 PM booking to 4:00 PM
1. Access the edit form
2. Change start time to 4:00 PM
3. Keep the same duration
4. Check that 4:00 PM is available
5. Save the changes

### Extending Duration
**Scenario**: Customer wants to extend from 2 hours to 3 hours
1. Open the edit form
2. Change duration from 2.0 to 3.0 hours
3. System checks if the extra hour is available
4. Review the additional cost
5. Save if acceptable

### Adding Utilities
**Scenario**: Customer decides they need sound system
1. Edit the reservation
2. Check the "Sound System" utility
3. Enter quantity needed
4. Review the additional cost
5. Save the changes

### Changing Fields
**Scenario**: Customer wants to switch from Soccer Field to Multi-Purpose
1. Edit the reservation
2. Select "Multi-Purpose Field" from dropdown
3. System rechecks availability for new field
4. Review any cost differences
5. Save if the new field is available

## Troubleshooting Edit Issues

### "Cannot Edit Reservation"
**Possible Reasons:**
- **24-Hour Rule**: Too close to booking time
- **Not Your Reservation**: You didn't create this booking
- **Wrong Status**: Reservation is cancelled or completed
- **System Error**: Try refreshing the page

### "Time Slot Not Available"
**Solutions:**
- **Check Conflicts**: Another reservation may exist at the new time
- **Try Alternative Times**: Look for nearby available slots
- **Different Date**: Consider changing the date instead
- **Contact Admin**: For assistance with complex scheduling

### "Edit Button Missing"
**Check These:**
- **Ownership**: Ensure you created the reservation
- **Time Limit**: Verify it's more than 24 hours away
- **Status**: Confirm reservation is not cancelled
- **Permissions**: Contact admin if you should have access

### Cost Calculation Errors
**Troubleshooting:**
- **Refresh Page**: Try reloading the edit form
- **Clear Changes**: Reset form and try again
- **Contact Support**: If costs seem incorrect
- **Check Utilities**: Verify utility selections are correct

## Best Practices for Editing

### Planning Changes
- **Edit Early**: Make changes as soon as you know about them
- **Check Calendar**: Review availability before editing
- **Communicate**: Inform customers about changes
- **Backup Plans**: Have alternative times ready

### Avoiding Problems
- **Double-Check**: Verify all changes before saving
- **Time Awareness**: Remember the 24-hour rule
- **Cost Review**: Understand cost implications
- **Customer Approval**: Confirm changes with customer first

### Efficient Editing
- **Batch Changes**: Make all changes at once rather than multiple edits
- **Use Calendar**: Check availability visually before editing
- **Quick Access**: Bookmark frequently edited reservations

## Tips for Successful Edits

### Before Making Changes
- **Confirm with Customer**: Get approval for changes before editing
- **Check Alternative Times**: Have backup options ready
- **Review Policies**: Remember the 24-hour modification rule
- **Consider Costs**: Understand financial implications

### During Editing
- **Work Systematically**: Make one change at a time
- **Verify Availability**: Check each change for conflicts
- **Monitor Costs**: Watch how changes affect total price
- **Save Frequently**: Don't lose work due to timeouts

### After Editing
- **Confirm Changes**: Verify all modifications were saved correctly
- **Notify Customer**: Inform them of the changes made
- **Update Records**: Note any important changes for future reference
- **Follow Up**: Ensure customer is satisfied with modifications

---

**Next Steps**:
- Learn about [Cancelling and Restoring Reservations](cancelling-restoring-reservations.md) if you need to cancel
- See [Calendar View Guide](viewing-reservations-calendar.md) to check availability
- Check [List View Guide](viewing-reservations-list.md) to manage multiple reservations
