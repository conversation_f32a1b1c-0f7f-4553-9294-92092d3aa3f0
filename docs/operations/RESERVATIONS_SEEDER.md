# Reservations Seeder Documentation

## Overview

The `ExistingReservationsSeeder` is a Laravel database seeder designed to export existing reservation data from the database and recreate it with updated dates. This is particularly useful for:

- Migrating production/development data to fresh database instances
- Creating test data that reflects real-world usage patterns
- Maintaining relative time intervals between reservations while updating dates to be current

## Features

- **Date Adjustment**: Automatically updates all reservation dates so the earliest reservation starts from today's date
- **Relationship Preservation**: Maintains all relationships with fields, users, and utilities
- **Duplicate Prevention**: Checks for existing reservations to avoid duplicates
- **Interactive Confirmation**: Asks before clearing existing data
- **Utility Support**: Preserves utility attachments with their rates and costs
- **Comprehensive Logging**: Provides detailed feedback during the seeding process

## Files

### 1. ExistingReservationsSeeder.php
Location: `database/seeders/ExistingReservationsSeeder.php`

The main seeder file containing hardcoded reservation data based on the current database state as of August 2025.

### 2. ExportReservationsSeeder Command
Location: `app/Console/Commands/ExportReservationsSeeder.php`

An Artisan command that dynamically generates a seeder file from the current database state.

## Usage

### Running the Existing Seeder

```bash
# Run the seeder directly
php artisan db:seed --class=ExistingReservationsSeeder

# Or include it in DatabaseSeeder.php and run all seeders
php artisan db:seed
```

### Generating a New Seeder from Current Data

```bash
# Export current reservations to a new seeder file
php artisan reservations:export-seeder

# Specify a custom filename
php artisan reservations:export-seeder --file=MyCustomReservationsSeeder.php
```

### Including in DatabaseSeeder

To include the seeder in your main database seeding process, uncomment the line in `database/seeders/DatabaseSeeder.php`:

```php
$this->call([
    UserRoleSeeder::class,
    AmenitySeeder::class,
    UtilitySeeder::class,
    FPMPFieldSeeder::class,
    ExistingReservationsSeeder::class,  // Uncomment this line
]);
```

## How It Works

### Date Adjustment Algorithm

1. **Find Earliest Date**: Identifies the earliest booking date in the dataset
2. **Calculate Difference**: Determines how many days to adjust all dates
3. **Apply Adjustment**: Adds/subtracts days to make the earliest reservation start from today
4. **Preserve Intervals**: Maintains the relative time differences between all reservations

### Example

If your original data has reservations from:
- 2025-08-10 (earliest)
- 2025-08-15
- 2025-08-20

And today is 2025-08-12, the seeder will adjust dates to:
- 2025-08-12 (today)
- 2025-08-17 (5 days later)
- 2025-08-22 (10 days later)

### Duplicate Prevention

The seeder checks for existing reservations with the same:
- Field ID
- User ID
- Booking date
- Start time
- End time

If a match is found, it skips creating that reservation and logs a warning.

## Data Structure

Each reservation in the seeder contains:

```php
[
    'field_name' => 'Veld Futbol',
    'user_email' => '<EMAIL>',
    'booking_date' => '2025-08-10',
    'start_time' => '15:00',
    'end_time' => '16:00',
    'duration_hours' => 1.0,
    'total_cost' => 65.00,
    'status' => 'Complete',
    'customer_name' => 'Pacheko',
    'customer_email' => '<EMAIL>',
    'customer_phone' => '+59990000000',
    'special_requests' => null,
    'admin_notes' => null,
    'confirmed_at' => '2025-08-09 00:25:50',
    'cancelled_at' => null,
    'utilities' => [
        ['name' => 'Paña di mesa', 'hours' => 1, 'rate' => 15.00, 'cost' => 15.00]
    ]
]
```

## Dependencies

The seeder requires the following to exist in the database:
- **Fields**: Referenced by name (e.g., 'Veld Futbol', 'Veld Bolas')
- **Users**: Referenced by email address
- **Utilities**: Referenced by name for utility attachments

If any dependencies are missing, the seeder will log warnings and skip those reservations.

## Best Practices

1. **Run Field and User Seeders First**: Ensure all required fields and users exist before running the reservations seeder
2. **Backup Before Clearing**: Always backup your database before using the clear option
3. **Test in Development**: Test the seeder in a development environment before using in production
4. **Update Regularly**: Regenerate the seeder periodically to reflect current data patterns
5. **Review Generated Data**: Check the generated seeder file before committing to version control

## Troubleshooting

### Common Issues

1. **Missing Fields/Users**: Ensure all referenced fields and users exist in the database
2. **Utility Not Found**: Check that all utility names in the seeder match existing utilities
3. **Date Conflicts**: If running multiple times, existing reservations may conflict with new ones

### Error Messages

- `Field 'FieldName' not found. Skipping reservation.`
- `User '<EMAIL>' not found. Skipping reservation.`
- `Utility 'UtilityName' not found for reservation ID.`
- `Reservation already exists for Field on Date at Time. Skipping.`

## Maintenance

To keep the seeder current:

1. **Regular Updates**: Use the export command to generate fresh seeders from current data
2. **Version Control**: Commit updated seeder files to track changes over time
3. **Documentation**: Update this documentation when adding new features or changing behavior

## Example Output

```
Starting ExistingReservationsSeeder...
Adjusting dates: earliest was 2025-08-10, now starting from 2025-08-12
Date adjustment: -2 days
Created reservation: Veld Futbol on 2025-08-08 at 15:00-16:00 for Andy Janga
Created reservation: Veld Futbol on 2025-08-13 at 14:30-15:30 for Andy Janga
...
ExistingReservationsSeeder completed successfully!
Created 11 reservations with updated dates.
```
